**Checklist — comparison workflow**

* **Gather feature details** for “prompt improver”/“enhance prompt” across: Roo Code, Kilo Code, Augment Code, OpenAI’s GPT‑5 Prompt Optimizer (as referenced in the Cookbook article), and v0.dev’s *AI prompt enhancement*. ([Roo Code Docs][1])
* **Normalize a feature schema**: trigger/UX, what gets optimized, sources of context, editability/transparency, routing/cost controls, versioning/reuse, notable constraints.
* **Fill a structured comparison** using the schema (table + bullets).
* **Add a focused analysis** of v0.dev’s enhancement using the same schema. ([v0][2])
* **Validate coverage & alignment** with the referenced GPT‑5 Prompt Optimizer article (OpenAI Cookbook) and official product docs. ([OpenAI Cookbook][3])

---

## Structured comparison (feature schema)

```mermaid
mindmap
  root((Prompt Enhancers — Comparison))
    Roo Code — "Enhance Prompt"
      Trigger / UX: Wand icon in chat input; enhances before send; spinner + tooltip; undo with Ctrl/Cmd‑Z. ([Roo Code Docs][1])
      Optimizes: Rewrites prompt for clarity, adds instructions, can add relevant project context (e.g., file path/selection). ([Roo Code Docs][1])
      Context sources: Optional use of last 10 messages ("task history") for context. Template is editable. ([Roo Code Docs][1])
      Editability & transparency: Replaces input with the enhanced text for user review; explicit UI affordances but no automatic diff view. ([Roo Code Docs][1])
      Model routing / cost controls: Enhancement can use a separate API configuration (provider/model) from normal chats. ([Roo Code Docs][1])
      Versioning & reuse: You can edit the enhancement template and test it in Settings; behaves like a reusable prompt macro. ([Roo Code Docs][1])
      Notes: Labeled "experimental"; quality varies by model; works even when message sending is queued. ([Roo Code Docs][1])
    Kilo Code — "Enhance Prompt"
      Trigger / UX: ✨ button in the UI; mirrors Roo’s UX (Kilo is a Roo fork). ([GitHub][2])
      Optimizes: Same intent—rewrite & structure user prompt. Community threads reference a dedicated ENHANCE support prompt. ([GitHub][3])
      Context sources: Discussions request using Memory Bank and richer context; Kilo exposes broad prompt/template controls (modes/system prompt). ([GitHub][4])
      Editability & transparency: Replace‑in‑place (no official diff); issues show enhance failures were fixed in later release. ([GitHub][5])
      Model routing / cost controls: Community guidance + maintainer replies point to separate provider/model config for enhancement; videos show using cheaper/faster models. ([GitHub][3])
      Versioning & reuse: Templates and modes are editable; settings surface shows provider info/pricing to guide routing choices. ([DeepWiki][6])
      Notes: Active development; docs distributed across code/DeepWiki; features may trail Roo or diverge. ([DeepWiki][7])
    Augment Code — "Prompt Enhancer" (Chat & CLI)
      Trigger / UX: VS Code/JetBrains ✨ and Auggie CLI Ctrl+P; enhancement occurs before sending. ([Augment Code][8])
      Optimizes: Expands a terse request into a structured, codebase‑aware spec (files, conventions, architecture hints). ([docs.augmentcode.com][9])
      Context sources: Pulls workspace context + conversation history; uses current project to seed specifics. ([docs.augmentcode.com][9])
      Editability & transparency: Transparent—"Prompt diff is transparent—what you see is what gets sent"; user reviews/edits before submit. ([Augment Code][8])
      Model routing / cost controls: Not documented as separate model routing in UI; enhancement is a distinct action; platform‑wide model lists exist. ([docs.augmentcode.com][9])
      Versioning & reuse: IDE‑level feature; no prompt object artifact, but enhanced prompt lives in history and can be reused. ([docs.augmentcode.com][9])
      Notes: Designed for coding tasks; available across VS Code and JetBrains. ([Augment Code][8])
    OpenAI — GPT‑5 Prompt Optimizer (Playground)
      Trigger / UX: Optimize button in Playground; opens an optimization panel with inline comments. ([OpenAI Cookbook][10])
      Optimizes: Cleans contradictions, clarifies formats, and tunes for target model/task (agentic, coding, multimodal). ([OpenAI Cookbook][10])
      Context sources: No project workspace by default; operates on the prompt you provide; optimization know‑how comes from OpenAI best practices. ([OpenAI Cookbook][10])
      Editability & transparency: Shows what changed and why; you review the optimized text. ([OpenAI Cookbook][10])
      Model routing / cost controls: Not a router; intended to improve the prompt you’ll then run with any GPT‑5 variant. ([OpenAI Cookbook][10])
      Versioning & reuse: Save as a Prompt Object with versions for reuse/migration. ([OpenAI Cookbook][10])
      Notes: Feature introduced in the OpenAI Cookbook article; used for migration & measurable gains. ([OpenAI Cookbook][10])
    v0.dev — AI prompt enhancement
      Trigger / UX: After writing a simple prompt, v0 offers an enhancement option to expand it into a fuller spec. ([v0][11])
      Optimizes: Adds concrete requirements (features, persistence, responsiveness, etc.) to improve generation quality for UI/apps. ([v0][11])
      Context sources: Draws from your textual prompt; docs don’t state codebase/context ingestion for enhancement. ([v0][11])
      Editability & transparency: User chooses to apply the enhancement; no diff UI documented, examples show before→after phrasing. ([v0][11])
      Model routing / cost controls: No model routing controls exposed for the enhancement step in docs. ([v0][11])
      Versioning & reuse: Enhancement is part of v0’s prompt‑to‑generate workflow; not versioned like OpenAI Prompt Objects. ([v0][11])
      Notes: Aimed at shipping UI/apps quickly; enhanced example: "Make a todo app" → detailed feature list. ([v0][11])
```

---

## Key similarities and differences

**Similarities**

* All five provide a **pre‑send rewrite** to improve clarity and specificity of your request. ([Roo Code Docs][1])
* Each supports **human-in-the-loop review** before execution (review/accept/edit). The degree of transparency varies (from v0’s simple apply to Augment’s explicit “what you send is what you see” and OpenAI’s change explanations). ([Augment Code][10])

**Differences that matter in practice**

* **Source of context**

  * **Code-aware**: Augment injects workspace, conventions, files; Roo can include recent chat history and selection/file path; Kilo discussions seek Memory Bank leverage. ([docs.augmentcode.com][11])
  * **Model‑best‑practices aware**: OpenAI’s Optimizer encodes GPT‑5 guidance (contradictions removal, format tightening) but is **not workspace‑aware**. v0’s enhancement expands the spec for UI builds without project context. ([OpenAI Cookbook][3])

* **Transparency style**

  * **Explains changes**: OpenAI Optimizer surfaces changes and rationale. ([OpenAI Cookbook][3])
  * **Shows exact outgoing text**: Augment emphasizes transparent, editable diffs; Roo/Kilo replace your input but don’t present an explicit diff. ([Augment Code][10])

* **Routing & cost control**

  * **Per‑feature model selection**: Roo lets you bind Enhance to a distinct provider/model; Kilo’s community guidance and tutorials emphasize using **cheaper/faster models** specifically for enhancement. ([Roo Code Docs][1])
  * **No routing (by design)**: OpenAI’s Optimizer optimizes the prompt itself; v0 docs don’t expose routing for the enhancement step. ([OpenAI Cookbook][3])

* **Reusability**

  * **Prompt artifacts**: OpenAI lets you **save versioned Prompt Objects**; Roo/Kilo provide **editable enhancement templates**; Augment stores enhanced prompts in history; v0 treats enhancement as an inline step. ([OpenAI Cookbook][3])

* **Scope / target task**

  * **Coding agents**: Roo, Kilo, Augment are IDE/agent‑centric with coding context. ([Roo Code Docs][1])
  * **General prompt migration**: OpenAI’s Optimizer serves any GPT‑5 workload (agentic, coding, multimodal) and is model‑centric. ([OpenAI Cookbook][3])
  * **UI/app generation**: v0’s enhancement specifically strengthens **product/UI specifications** to improve generated apps. ([v0][2])

---

## Focused analysis — v0.dev’s *AI prompt enhancement* (same schema)

* **Trigger/UX:** After you type a simple instruction, v0 **offers an enhancement**; selecting it expands your prompt into a comprehensive, implementation‑oriented spec. ([v0][2])
* **What it optimizes:** Converts a casual idea (“Make a todo app”) into **explicit features and requirements** (CRUD ops, persistence, responsiveness), which materially improves generation fidelity for UI/apps. ([v0][2])
* **Context sources:** Enhancement uses your textual input; unlike IDE agents, docs don’t state codebase ingestion for the enhancement step. (v0 can build full‑stack apps, but that’s downstream of the enhanced prompt.) ([v0][2])
* **Editability/transparency:** User chooses to apply the enhancement and can continue iterating; the docs don’t describe a change‑explanation UI like OpenAI’s Optimizer or Augment’s explicit diff. ([v0][2])
* **Routing/cost controls:** Not documented for the enhancement action; the emphasis is on better **specs** rather than per‑action model selection. ([v0][2])
* **Versioning/reuse:** Enhancement is part of the prompting flow; no versioned “prompt object” concept is documented. ([v0][2])
* **Net effect:** For v0’s audience (shipping UI fast), the enhancement step **front‑loads requirements** so generations are closer to what you’d put in a PRD—lightweight, guided planning without leaving the builder. ([v0][2])

---

## Notes on each product (quick references)

* **Roo Code**: Customizable **ENHANCE** template, separate API config for Enhance, optional inclusion of last **10** messages, and explicit UI affordances (spinner, tooltip). ([Roo Code Docs][1])
* **Kilo Code**: Roo‑derived feature set; community docs and maintainers discuss **separate model** for Enhance and template customization; “enhance failed for gpt‑5” bug fixed in v4.79.0. ([GitHub][5])
* **Augment Code**: ✨/**Ctrl+P**; **workspace‑aware**; transparent “what you send is what you see”; available in **VS Code & JetBrains**. ([docs.augmentcode.com][11])
* **OpenAI GPT‑5 Prompt Optimizer**: Introduced in the **OpenAI Cookbook** article; Optimize panel shows **what changed and why**; supports saving **Prompt Objects** for reuse/migration. ([OpenAI Cookbook][3])
* **v0.dev**: “**AI prompt enhancement**” upgrades terse instructions to **implementation‑ready specs**; integrated into text prompting. ([v0][2])

---

## Validation — coverage & alignment

* **Products covered:** Roo Code, Kilo Code, Augment Code, OpenAI’s GPT‑5 Prompt Optimizer (as referenced in the OpenAI **Cookbook** article), and v0.dev’s AI prompt enhancement. ✔️ ([Roo Code Docs][1])
* **Feature areas covered:** Trigger/UX, optimization goal, context sources, editability/transparency, routing/cost controls, versioning/reuse, and constraints—**for each** product. ✔️
* **Alignment with the referenced article:** The description of OpenAI’s GPT‑5 Prompt Optimizer (Optimize panel, change explanations, and Prompt Objects for reuse/migration) matches the **OpenAI Cookbook** article introducing the feature (Aug 7, 2025). ✔️ ([OpenAI Cookbook][3])
* **Source grounding:** Where vendor docs exist, they’re cited (Roo, Augment, v0, OpenAI). For Kilo (active fork), specifics on enhancement configuration are corroborated by maintainer/community discussions and the project README, with deep‑dive docs on prompts/modes. ✔️ ([GitHub][5])

If you’d like, I can tailor the table to your exact decision criteria (e.g., corporate policy on model routing, transparency requirements, or IDE footprint) and add a “recommended fit” row per team.

[1]: https://docs.roocode.com/features/enhance-prompt/ "Enhance Prompt | Roo Code Documentation"
[2]: https://v0.app/docs/text-prompting "Text Prompting | v0 Docs"
[3]: https://cookbook.openai.com/examples/gpt-5/prompt-optimization-cookbook "GPT-5 Prompt Migration and Improvement Using the New Optimizer"
[4]: https://github.com/Kilo-Org/kilocode?utm_source=chatgpt.com "GitHub - Kilo-Org/kilocode: Open Source AI coding assistant for ..."
[5]: https://github.com/Kilo-Org/kilocode/discussions/359 "Feature request prompt and AI provider for enhancement customization · Kilo-Org kilocode · Discussion #359 · GitHub"
[6]: https://github.com/Kilo-Org/kilocode/discussions/713?utm_source=chatgpt.com "Ensure enhance prompt leverages Memory Bank if enabled · Kilo-Org ..."
[7]: https://github.com/Kilo-Org/kilocode/issues/1852?utm_source=chatgpt.com "Failed to enhance prompt for gpt-5 · Issue #1852 · Kilo-Org/kilocode"
[8]: https://deepwiki.com/Kilo-Org/kilocode/4.1-prompt-and-mode-configuration?utm_source=chatgpt.com "Settings Interface and Model Selection | Kilo-Org/kilocode | DeepWiki"
[9]: https://deepwiki.com/Kilo-Org/kilocode/3.1-system-prompt-generation?utm_source=chatgpt.com "System Prompt Generation and Tool System | Kilo-Org/kilocode | DeepWiki"
[10]: https://www.augmentcode.com/blog/prompt-enhancer-live-in-augment-chat "Prompt Enhancer live in Augment Chat - Augment Code"
[11]: https://docs.augmentcode.com/cli/interactive/prompt-enhancer "Prompt Enhancer - Augment"

