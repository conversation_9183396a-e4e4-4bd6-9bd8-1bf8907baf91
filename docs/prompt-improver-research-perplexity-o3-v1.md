# Comparing “Prompt Improver” Workflows

```mermaid
mindmap
  root((Prompt Improver Workflows))
    Roo Code “Enhance Prompt”
      Where
        VS Code / JetBrains extension, CLI
      Main Goals
        1) Add missing context
        2) Remove ambiguity
        3) Keep tokens small
      Build Final Prompt
        Runs a local prompt-template that asks the LLM to: “Identify gaps, add relevant file context, list assumptions, then output a concise enhanced prompt only.”[1][2]
        Pulls recent chat plus selected code snippets (not full repo) to avoid overload[3]
      Customisation & Cost
        You pick the model for enhancement; many route to a cheap/fast model (e.g., GPT-4-Nano) to cut costs and latency[4]
        Template file is editable; can be committed per-project for shared house style[5]
      Extras
        Mode-aware templates for “Code”, “Ask”, “Architect”, etc.[6]
        UI shows a prompt diff so you can inspect exactly what will be sent

    Kilo Code “Enhance Prompt”
      Where
        VS Code extension
      Main Goals
        Same three goals as Roo, aimed at lighter-weight workflows
      Build Final Prompt
        Single-click rewrite adds headings like “Goal”, “Constraints”, “Output_Format”, and inlines the active file path if relevant[2]
      Customisation & Cost
        Assign a cheap enhancement profile while main chat uses a stronger model — reported 2× faster and up to 30× cheaper[4][7]
      Extras
        Consistent scaffold so downstream agents parse it easily

    Augment Code “Prompt Enhancer”
      Where
        VS Code & JetBrains, plus CLI
      Main Goals
        Inject codebase context from a real-time index
        Expose model’s interpretation
      Build Final Prompt
        Ctrl+P freezes input; enhancement attaches retrieved symbols, file paths, and a task checklist[8][9]
        Side-by-side diff to fix misunderstandings before sending[9]
      Customisation & Cost
        Uses Augment’s semantic index; retrieval is automatic, not file-chosen by user
        No per-prompt model choice; uses the workspace default
      Extras
        Strongest on deep repo context; often includes function signatures or README excerpts automatically[10]

    OpenAI GPT-5 Prompt Optimizer
      Where
        Web console & API (any app)
      Main Goals
        Rewrite into canonical “Role → Task → Output Checks”
        Resolve conflicting instructions
        A/B test variants
      Build Final Prompt
        Paste any prompt, select priorities (accuracy, brevity, creativity, safety); adds validation steps and may include a reasoning-depth slider indicator[11][12]
      Customisation & Cost
        Not tied to an IDE; can be scripted via API
        Stores optimized prompts as “Prompt Objects” for reuse
      Extras
        Built-in A/B harness to compare prompts on live tasks
        Export to JSON Schema for programmatic use[11]
```

## Key Similarities

- **Objective**: All four aim to turn a rough, human-friendly request into a structured, unambiguous, and token-efficient prompt.
- **Transparency**: Each surfaces the diff so you can review before sending (Roo, Augment, GPT-5) or simply replaces the text in-place (Kilo).

## Important Differences

1. **Context Retrieval**
   - Augment pulls *repo-wide* context automatically via its index—best when you forget which files matter.[10]
   - Roo limits itself to your last chat turns and currently open/selected files to keep prompts short.[3]
   - Kilo adds only the active file path or selection.[2]
   - GPT-5 Optimizer relies on *you* to supply context; it doesn’t fetch code.

2. **Cost & Speed Controls**
   - Roo and Kilo let you route enhancement to a cheaper model independently of the main chat, giving tangible savings (4 s vs. 9 s round-trip in Kilo’s user report).[4]
   - GPT-5 Optimizer charges normal GPT-5 rates; no separate “cheap” path.
   - Augment has fixed pricing tied to its agent run; you can’t swap models just for enhancement.

3. **Template Flexibility**
   - Roo exposes its enhancement template as a markdown file, so teams can version-control house rules (e.g., always ask for a test plan).[5]
   - Kilo’s scaffold is fixed but consistent; you can’t edit the underlying template.
   - Augment’s enhancer is closed-config, but you influence what gets pulled by adjusting the index scope.
   - GPT-5 Optimizer is configurable via UI sliders and checkboxes, not raw template editing.

4. **Advanced Features**
   - GPT-5 Optimizer’s A/B testing and “Prompt Objects” make it a mini prompt-management platform.[11]
   - Roo’s *mode-aware* prompting means the enhancer understands whether you’re in “Architect” vs. “Debug” mode and tweaks the structure accordingly.[6]
   - Augment shows the model’s inferred plan inline, letting you catch misinterpretations early.[9]

## When to Choose Which

- **Solo devs on open-source editors**: Roo or Kilo—both free, configurable, and cheap to run. Pick Roo if you need multiple specialized modes; choose Kilo for the simplest UX.
- **Teams with large codebases**: Augment—repo-aware enhancement saves you from manual context curation.
- **Language-agnostic prompt experimentation**: GPT-5 Optimizer—use it when you need platform-neutral, shareable prompts or formal A/B evidence.

## Practical Tip

Even if you adopt Roo, Kilo, or Augment inside the IDE, you can still pipe the resulting prompt through GPT-5’s Optimizer for a second-pass polish when the task is critical (e.g., production migrations). The Optimizer’s conflict-resolution heuristics often catch subtle instruction clashes that IDE tools overlook.[12]

***

**Bottom line:**  
Roo Code and Kilo Code focus on quick, low-cost prompt cleanup inside the editor; Augment Code goes deeper by injecting semantic code context; OpenAI’s GPT-5 Prompt Optimizer is a heavyweight, model-agnostic polishing and testing suite best suited for high-stakes or reusable prompts.

[1](https://www.reddit.com/r/RooCode/comments/1k0ibll/what_are_your_promptenhancing_prompts_heres_the/)
[2](https://kilocode.ai/docs/features/enhance-prompt)
[3](https://www.youtube.com/shorts/z0wEJngEe2Y)
[4](https://www.reddit.com/r/kilocode/comments/1kqc4g3/2x_faster_enhance_prompt_feature_tuning/)
[5](https://github.com/RooCodeInc/Roo-Code/issues/3754)
[6](https://www.youtube.com/watch?v=rg_g3BPv4uQ)
[7](https://blog.kilocode.ai/p/2x-faster-prompt-enhancement-in-kilo)
[8](https://docs.augmentcode.com/cli/interactive/prompt-enhancer)
[9](https://www.augmentcode.com/blog/prompt-enhancer-live-in-augment-chat)
[10](https://www.lukew.com/ff/entry.asp?2101)
[11](https://www.reddit.com/r/ChatGPTPromptGenius/comments/1mnqxau/stop_wasting_tokens_gpt5s_new_prompt_optimizer/)
[12](https://khemaridh.substack.com/p/i-have-no-idea-how-to-prompt-gpt)
[13](https://github.com/RooCodeInc/Roo-Code/issues/4099)
[14](https://www.youtube.com/watch?v=mwJx5QI2c0o)
[15](https://www.youtube.com/watch?v=FvTPcfSKsVs)
[16](https://qubika.com/blog/roo-code/)
[17](https://www.youtube.com/watch?v=R1nDnCK-xzw)
[18](https://www.augmentcode.com)
[19](https://cookbook.openai.com/examples/gpt-5/prompt-optimization-cookbook)
[20](https://docs.roocode.com/update-notes/v3.18.3)
[21](https://kilocode.ai)
[22](https://www.reddit.com/r/AugmentCodeAI/comments/1kvuf5b/first_time_using_augment/)
[23](https://platform.openai.com/chat/edit?models=gpt-5&optimize=true)
[24](https://www.reddit.com/r/ChatGPTCoding/comments/1i8l5aj/do_you_use_rooclines_or_bolts_enhance_prompt/)
[25](https://github.com/RooCodeInc/Roo-Code)
[26](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
[27](https://www.reddit.com/r/ClaudeAI/comments/1m9nz8m/does_cc_have_a_prompt_enhancement_feature_similar/)
[28](https://www.youtube.com/watch?v=_Rs1kmaUlIQ)
[29](https://cookbook.openai.com/examples/gpt-5/gpt-5_prompting_guide)
[30](https://www.reddit.com/r/RooCode/comments/1iprp4m/whats_the_secret_sauce_of_the_prompt_enhancer/)
[31](https://aws.amazon.com/blogs/architecture/how-smartsheet-boosts-developer-productivity-with-amazon-bedrock-and-roo-code/)
[32](https://portkey.ai/blog/how-to-use-roo-code-in-your-organization)
[33](https://www.youtube.com/watch?v=tJCilwS5GOo)
